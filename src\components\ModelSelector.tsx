import React, { useState, useMemo } from 'react'
import { useAppStore } from '../store'
import { useNetworkStore } from '../stores/networkStore'
import { Search, Star, Brain, Code, Eye, Gift, ChevronDown, Check, Ship } from './Icons'
import {
  modelCategories,
  enhanceModelInfo,
  searchModels,
  sortModels,
  getRecommendedModels,
  formatPricing,
  formatContextLength
} from '../utils/modelUtils'
import { OpenRouterModel } from '../types'

interface ModelSelectorProps {
  selectedModel?: string
  onModelSelect: (modelId: string) => void
  className?: string
}

const ModelSelector: React.FC<ModelSelectorProps> = ({
  selectedModel,
  onModelSelect,
  className = ''
}) => {
  const { models } = useAppStore()
  const { isPrivateMode, localModels, localModelsAvailable } = useNetworkStore()
  const [isOpen, setIsOpen] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [sortBy] = useState<'name' | 'provider' | 'price' | 'context'>('name')
  const [favoriteModels, setFavoriteModels] = useState<string[]>([])

  // Load favorite models from database on mount
  useEffect(() => {
    const loadFavoriteModels = async () => {
      if (window.electronAPI?.favoriteModels) {
        try {
          const favorites = await window.electronAPI.favoriteModels.get()
          setFavoriteModels(favorites)
        } catch (error) {
          console.error('Failed to load favorite models:', error)
        }
      }
    }
    loadFavoriteModels()
  }, [])

  // Get available models based on private mode
  const availableModels = useMemo(() => {
    if (isPrivateMode) {
      // In private mode, only show local models
      return localModels.map(localModel => ({
        id: localModel.id,
        name: localModel.name,
        description: `Local ${localModel.provider} model`,
        pricing: { prompt: "0", completion: "0", image: "0", request: "0" },
        context_length: 4096, // Default context length for local models
        architecture: { modality: "text", tokenizer: "unknown", instruct_type: null },
        top_provider: { context_length: 4096, max_completion_tokens: null },
        per_request_limits: null
      })) as OpenRouterModel[]
    }
    return models
  }, [isPrivateMode, localModels, models])

  // Filter and search models
  const filteredModels = useMemo(() => {
    let filtered = availableModels

    // Apply category filter
    const category = modelCategories.find(cat => cat.id === selectedCategory)
    if (category) {
      filtered = filtered.filter(model => category.filter(model, favoriteModels))
    }

    // Apply search
    if (searchQuery.trim()) {
      filtered = searchModels(filtered, searchQuery)
    }

    // Sort models
    return sortModels(filtered, sortBy)
  }, [availableModels, selectedCategory, searchQuery, sortBy, favoriteModels])

  // Get recommended models for quick access
  const recommendedModels = useMemo(() => {
    return getRecommendedModels(availableModels).slice(0, 4)
  }, [availableModels])

  const selectedModelInfo = availableModels.find(m => m.id === selectedModel)

  const getCategoryIcon = (categoryId: string) => {
    switch (categoryId) {
      case 'free': return <Gift className="h-4 w-4" />
      case 'flagship': return <Ship className="h-4 w-4" />
      case 'reasoning': return <Brain className="h-4 w-4" />
      case 'code': return <Code className="h-4 w-4" />
      case 'vision': return <Eye className="h-4 w-4" />
      default: return null
    }
  }

  const toggleFavorite = async (modelId: string, e: React.MouseEvent) => {
    e.stopPropagation()

    if (!window.electronAPI?.favoriteModels) {
      console.error('Favorite models API not available')
      return
    }

    try {
      const isFavorite = favoriteModels.includes(modelId)

      if (isFavorite) {
        await window.electronAPI.favoriteModels.remove(modelId)
        setFavoriteModels(prev => prev.filter(id => id !== modelId))
      } else {
        await window.electronAPI.favoriteModels.add(modelId)
        setFavoriteModels(prev => [...prev, modelId])
      }
    } catch (error) {
      console.error('Failed to toggle favorite model:', error)
    }
  }

  const ModelCard: React.FC<{ model: OpenRouterModel; isSelected: boolean }> = ({ model, isSelected }) => {
    const enhanced = enhanceModelInfo(model)
    const isFavorite = favoriteModels.includes(model.id)
    
    return (
      <div
        className={`
          p-3 rounded-lg border cursor-pointer transition-all hover:bg-neutral-800/50
          ${isSelected 
            ? 'border-indigo-500 bg-indigo-500/10' 
            : 'border-neutral-700 hover:border-neutral-600'
          }
        `}
        onClick={() => {
          onModelSelect(model.id)
          setIsOpen(false)
        }}
      >
        <div className="flex items-start justify-between mb-2">
          <div className="flex justify-between items-start">
            <div className="font-medium text-sm flex-1">{enhanced.displayName}</div>
            <button 
              onClick={(e) => toggleFavorite(model.id, e)}
              className="text-neutral-400 hover:text-yellow-400 transition-colors"
              aria-label={isFavorite ? 'Remove from favorites' : 'Add to favorites'}
            >
              <Star className={`h-4 w-4 ${isFavorite ? 'fill-yellow-400 text-yellow-400' : 'text-neutral-500'}`} />
            </button>
          </div>
          <div className="flex justify-between items-center mt-1">
            <div className="flex items-center gap-2 text-xs text-neutral-400">
              <span>{model.id.split('/')[0]}</span>
              <span>•</span>
              <span>{formatContextLength(model.context_length)}</span>
            </div>
            <div className="flex items-center gap-1">
              {enhanced.isFree && <div title="Free"><Gift className="h-3 w-3 text-green-400" /></div>}
              {enhanced.isFlagship && <div title="Flagship"><Ship className="h-3 w-3 text-yellow-400" /></div>}
              {enhanced.isReasoning && <div title="Reasoning"><Brain className="h-3 w-3 text-purple-400" /></div>}
              {enhanced.isCode && <div title="Code"><Code className="h-3 w-3 text-blue-400" /></div>}
              {isSelected && <Check className="h-3 w-3 text-indigo-400" />}
            </div>
          </div>
        </div>

        <div className="flex items-center justify-between text-xs text-neutral-500">
          <span>{formatPricing(model)}</span>
        </div>
        
        {model.description && (
          <p className="text-xs text-neutral-400 mt-1 line-clamp-2">{model.description}</p>
        )}
      </div>
    )
  }

  return (
    <div className={`relative ${className}`}>
      {/* Trigger Button */}
      <button
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        className="input-field w-full flex items-center justify-between"
      >
        <div className="flex items-center gap-2 min-w-0">
          {selectedModelInfo ? (
            <>
              <div className="flex items-center gap-1">
                {enhanceModelInfo(selectedModelInfo).isFree && <Gift className="h-3 w-3 text-green-400" />}
                {enhanceModelInfo(selectedModelInfo).isFlagship && <Ship className="h-3 w-3 text-yellow-400" />}
              </div>
              <span className="truncate">{selectedModelInfo.name}</span>
            </>
          ) : (
            <span className="text-neutral-500">Select a model...</span>
          )}
        </div>
        <ChevronDown className={`h-4 w-4 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </button>

      {/* Dropdown */}
      {isOpen && (
        <div className="absolute top-full left-0 right-0 mt-1 bg-neutral-900 border border-neutral-700 rounded-lg shadow-xl z-50 max-h-96 overflow-hidden">
          {/* Header */}
          <div className="p-3 border-b border-neutral-700">
            {/* Private Mode Indicator */}
            {isPrivateMode && (
              <div className="mb-3 p-2 bg-secondary/20 border border-secondary/30 rounded-lg">
                <div className="flex items-center gap-2">
                  <i className="fa-solid fa-shield-halved text-secondary"></i>
                  <span className="text-sm font-medium text-secondary">Private Mode</span>
                </div>
                <p className="text-xs text-gray-400 mt-1">
                  {localModelsAvailable
                    ? `Showing ${localModels.length} local model${localModels.length !== 1 ? 's' : ''}`
                    : 'No local models available. Install Ollama or LM Studio to use private mode.'
                  }
                </p>
              </div>
            )}

            {/* Search */}
            <div className="relative mb-3">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-neutral-400" />
              <input
                type="text"
                placeholder="Search models..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 bg-neutral-800 border border-neutral-600 rounded-lg text-sm focus:outline-none focus:border-indigo-500"
              />
            </div>

            {/* Category Filters */}
            <div className="flex flex-wrap gap-1">
              {modelCategories.map((category) => (
                <button
                  key={category.id}
                  onClick={() => setSelectedCategory(category.id)}
                  className={`
                    px-2 py-1 rounded text-xs font-medium transition-colors flex items-center gap-1
                    ${selectedCategory === category.id
                      ? 'bg-indigo-500 text-white'
                      : 'bg-neutral-800 text-neutral-300 hover:bg-neutral-700'
                    }
                  `}
                >
                  {getCategoryIcon(category.id)}
                  {category.name}
                </button>
              ))}
            </div>
          </div>

          {/* Content */}
          <div className="max-h-64 overflow-y-auto">
            {/* Recommended Models (only show when no search/filter) */}
            {!searchQuery && selectedCategory === 'all' && recommendedModels.length > 0 && (
              <div className="p-3 border-b border-neutral-700">
                <h3 className="text-sm font-medium mb-2 text-neutral-300">Recommended</h3>
                <div className="grid grid-cols-1 gap-2">
                  {recommendedModels.map((model) => (
                    <ModelCard
                      key={model.id}
                      model={model}
                      isSelected={model.id === selectedModel}
                    />
                  ))}
                </div>
              </div>
            )}

            {/* All Models */}
            <div className="p-3">
              {filteredModels.length === 0 ? (
                <div className="text-center py-4 text-neutral-400">
                  <p>No models found</p>
                  {searchQuery && (
                    <button
                      onClick={() => setSearchQuery('')}
                      className="text-indigo-400 hover:text-indigo-300 text-sm mt-1"
                    >
                      Clear search
                    </button>
                  )}
                </div>
              ) : (
                <div className="space-y-2">
                  {filteredModels.map((model) => (
                    <ModelCard
                      key={model.id}
                      model={model}
                      isSelected={model.id === selectedModel}
                    />
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Backdrop */}
      {isOpen && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setIsOpen(false)}
        />
      )}
    </div>
  )
}

export default ModelSelector
