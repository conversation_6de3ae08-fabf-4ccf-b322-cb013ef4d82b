import { OpenRouterModel, EnhancedModelInfo, ModelCategory, ModelPreset } from '../types'

// Model categorization logic
export const enhanceModelInfo = (model: OpenRouterModel): EnhancedModelInfo => {
  const provider = model.id.split('/')[0] || 'unknown'
  const modelName = model.name.toLowerCase()
  const modelId = model.id.toLowerCase()
  
  // Check if model is free (pricing is "0" or "0.0")
  const isFree = model.pricing.prompt === "0" || 
                 model.pricing.completion === "0" ||
                 (parseFloat(model.pricing.prompt) === 0 && parseFloat(model.pricing.completion) === 0)
  
  // Flagship models - top-tier models from major providers
  const isFlagship = (
    modelId.includes('gpt-4') ||
    modelId.includes('claude-3.5-sonnet') ||
    modelId.includes('claude-3-opus') ||
    modelId.includes('gemini-1.5-pro') ||
    modelId.includes('gemini-2.0-flash') ||
    modelName.includes('flagship')
  )
  
  // Reasoning models - models designed for complex reasoning
  const isReasoning = (
    modelId.includes('o1-') ||
    modelId.includes('reasoning') ||
    modelName.includes('reasoning') ||
    modelName.includes('think') ||
    modelId.includes('deepseek-r1')
  )
  
  // Code-specialized models
  const isCode = (
    modelId.includes('code') ||
    modelId.includes('codestral') ||
    modelId.includes('deepseek-coder') ||
    modelId.includes('starcoder') ||
    modelName.includes('code')
  )
  
  // Vision-capable models
  const isVision = (
    modelId.includes('vision') ||
    modelId.includes('gpt-4o') ||
    modelId.includes('claude-3') ||
    modelId.includes('gemini') ||
    modelName.includes('vision')
  )
  
  // Calculate max tokens supported (use context_length as fallback)
  const maxTokensSupported = model.top_provider?.max_completion_tokens || 
                             Math.min(model.context_length * 0.75, 100000) // Conservative estimate
  
  return {
    ...model,
    isFree,
    isFlagship,
    isReasoning,
    isCode,
    isVision,
    provider,
    maxTokensSupported
  }
}

// Model categories for filtering
export const modelCategories: ModelCategory[] = [
  {
    id: 'all',
    name: 'All Models',
    description: 'Show all available models',
    filter: () => true,
    icon: '🤖'
  },
  {
    id: 'free',
    name: 'Free Models',
    description: 'Models with no cost per token',
    filter: (model) => enhanceModelInfo(model).isFree,
    icon: '🆓'
  },
  {
    id: 'flagship',
    name: 'Flagship',
    description: 'Top-tier models from major providers',
    filter: (model) => enhanceModelInfo(model).isFlagship,
    icon: '🚢'
  },
  {
    id: 'reasoning',
    name: 'Reasoning',
    description: 'Models optimized for complex reasoning tasks',
    filter: (model) => enhanceModelInfo(model).isReasoning,
    icon: '🧠'
  },
  {
    id: 'code',
    name: 'Code',
    description: 'Models specialized for programming tasks',
    filter: (model) => enhanceModelInfo(model).isCode,
    icon: '💻'
  },
  {
    id: 'vision',
    name: 'Vision',
    description: 'Models capable of processing images',
    filter: (model) => enhanceModelInfo(model).isVision,
    icon: '👁️'
  },
  {
    id: 'favorites',
    name: 'Favorites',
    description: 'Your bookmarked models',
    filter: (model, favoriteModels) => favoriteModels?.includes(model.id) || false,
    icon: '⭐'
  }
]

// Predefined model configuration presets
export const modelPresets: ModelPreset[] = [
  {
    name: 'Factual',
    description: 'Precise, deterministic responses for factual queries',
    temperature: 0.1,
    topP: 0.9,
    topK: 20,
    maxTokens: 4096
  },
  {
    name: 'Balanced',
    description: 'Good balance of creativity and accuracy',
    temperature: 0.7,
    topP: 0.95,
    topK: 30,
    maxTokens: 4096
  },
  {
    name: 'Creative',
    description: 'More diverse and creative outputs',
    temperature: 0.9,
    topP: 0.99,
    topK: 40,
    maxTokens: 4096
  },
  {
    name: 'Reasoning',
    description: 'Optimized for complex reasoning tasks',
    temperature: 0.2,
    topP: 0.95,
    topK: 25,
    maxTokens: 8192
  }
]

// Search and filter models
export const searchModels = (models: OpenRouterModel[], query: string): OpenRouterModel[] => {
  if (!query.trim()) return models
  
  const searchTerm = query.toLowerCase()
  return models.filter(model => 
    model.name.toLowerCase().includes(searchTerm) ||
    model.id.toLowerCase().includes(searchTerm) ||
    model.description?.toLowerCase().includes(searchTerm) ||
    enhanceModelInfo(model).provider.toLowerCase().includes(searchTerm)
  )
}

// Sort models by relevance/popularity
export const sortModels = (models: OpenRouterModel[], sortBy: 'name' | 'provider' | 'price' | 'context' = 'name'): OpenRouterModel[] => {
  return [...models].sort((a, b) => {
    const aEnhanced = enhanceModelInfo(a)
    const bEnhanced = enhanceModelInfo(b)
    
    // Prioritize flagship models
    if (aEnhanced.isFlagship && !bEnhanced.isFlagship) return -1
    if (!aEnhanced.isFlagship && bEnhanced.isFlagship) return 1
    
    switch (sortBy) {
      case 'provider':
        return aEnhanced.provider.localeCompare(bEnhanced.provider)
      case 'price':
        const aPrice = parseFloat(a.pricing.prompt) + parseFloat(a.pricing.completion)
        const bPrice = parseFloat(b.pricing.prompt) + parseFloat(b.pricing.completion)
        return aPrice - bPrice
      case 'context':
        return b.context_length - a.context_length
      default:
        return a.name.localeCompare(b.name)
    }
  })
}

// Get recommended models for new users
export const getRecommendedModels = (models: OpenRouterModel[]): OpenRouterModel[] => {
  const enhanced = models.map(enhanceModelInfo)
  
  // Prioritize free flagship models, then paid flagship models
  return enhanced
    .filter(model => model.isFlagship || model.isFree)
    .sort((a, b) => {
      if (a.isFree && a.isFlagship && !(b.isFree && b.isFlagship)) return -1
      if (!(a.isFree && a.isFlagship) && (b.isFree && b.isFlagship)) return 1
      if (a.isFree && !b.isFree) return -1
      if (!a.isFree && b.isFree) return 1
      return a.name.localeCompare(b.name)
    })
    .slice(0, 8) // Top 8 recommendations
}

// Format pricing for display
export const formatPricing = (model: OpenRouterModel): string => {
  const promptPrice = parseFloat(model.pricing.prompt)
  const completionPrice = parseFloat(model.pricing.completion)
  
  if (promptPrice === 0 && completionPrice === 0) {
    return 'Free'
  }
  
  const formatPrice = (price: number) => {
    if (price === 0) return '0'
    if (price < 0.001) return `$${(price * 1000000).toFixed(2)}/1M`
    if (price < 1) return `$${(price * 1000).toFixed(2)}/1K`
    return `$${price.toFixed(3)}`
  }
  
  return `${formatPrice(promptPrice)} / ${formatPrice(completionPrice)}`
}

// Get context length display
export const formatContextLength = (contextLength: number): string => {
  if (contextLength >= 1000000) {
    return `${(contextLength / 1000000).toFixed(1)}M`
  }
  if (contextLength >= 1000) {
    return `${(contextLength / 1000).toFixed(0)}K`
  }
  return contextLength.toString()
}
