import React, { useState, useEffect } from 'react'
import { createPortal } from 'react-dom'
import { useAppStore } from '../store'

interface ModelChangeToastProps {
  isVisible: boolean
  newModel: string
  previousModel: string
  onRevert: () => void
  onDismiss: () => void
  onOptOut: () => void
}

const ModelChangeToast: React.FC<ModelChangeToastProps> = ({
  isVisible,
  newModel,
  previousModel,
  onRevert,
  onDismiss,
  onOptOut
}) => {
  const [isAnimating, setIsAnimating] = useState(false)
  const { models } = useAppStore()

  useEffect(() => {
    if (isVisible) {
      setIsAnimating(true)
      // Auto-dismiss after 5 seconds if no action taken
      const timer = setTimeout(() => {
        handleDismiss()
      }, 5000)
      return () => clearTimeout(timer)
    }
  }, [isVisible])

  const handleDismiss = () => {
    setIsAnimating(false)
    setTimeout(onDismiss, 300) // Wait for animation to complete
  }

  const handleRevert = () => {
    setIsAnimating(false)
    setTimeout(onRevert, 300)
  }

  const handleOptOut = () => {
    setIsAnimating(false)
    setTimeout(onOptOut, 300)
  }

  // Get model names for display
  const getModelName = (modelId: string) => {
    if (modelId.includes(':')) {
      // Local model format: "provider:modelname"
      const [provider, name] = modelId.split(':')
      return `${name} (${provider})`
    }
    // External model - find in models list
    const model = models.find(m => m.id === modelId)
    return model ? model.name : modelId
  }

  if (!isVisible) return null

  const toastContent = (
    <div className={`
      fixed bottom-4 right-4 z-[9999] max-w-md
      transform transition-all duration-300 ease-out
      ${isAnimating ? 'translate-y-0 opacity-100' : 'translate-y-2 opacity-0'}
    `}>
      <div className="bg-gray-800 border border-gray-700 rounded-lg shadow-2xl backdrop-blur-sm">
        {/* Header */}
        <div className="flex items-center gap-3 p-4 border-b border-gray-700">
          <div className="w-8 h-8 bg-primary/20 rounded-full flex items-center justify-center">
            <i className="fa-solid fa-robot text-primary text-sm"></i>
          </div>
          <div className="flex-1">
            <h4 className="font-medium text-supplement1">Model Changed</h4>
            <p className="text-xs text-gray-400">Default model was automatically selected</p>
          </div>
          <button
            onClick={handleDismiss}
            className="text-gray-400 hover:text-supplement1 transition-colors"
          >
            <i className="fa-solid fa-xmark text-sm"></i>
          </button>
        </div>

        {/* Content */}
        <div className="p-4">
          <div className="space-y-3">
            <div className="text-sm">
              <div className="flex items-center gap-2 mb-1">
                <span className="text-gray-400">From:</span>
                <span className="text-supplement1 font-medium">{getModelName(previousModel)}</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-gray-400">To:</span>
                <span className="text-primary font-medium">{getModelName(newModel)}</span>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex gap-2 pt-2">
              <button
                onClick={handleRevert}
                className="flex-1 px-3 py-2 bg-primary hover:bg-primary/80 text-gray-900 font-medium rounded-lg text-sm transition-colors"
              >
                Revert
              </button>
              <button
                onClick={handleDismiss}
                className="flex-1 px-3 py-2 bg-gray-700 hover:bg-gray-600 text-supplement1 font-medium rounded-lg text-sm transition-colors"
              >
                Keep
              </button>
            </div>

            {/* Opt-out option */}
            <div className="pt-2 border-t border-gray-700">
              <button
                onClick={handleOptOut}
                className="w-full text-xs text-gray-400 hover:text-gray-300 transition-colors"
              >
                Don't show this message again
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )

  return createPortal(toastContent, document.body)
}

export default ModelChangeToast
