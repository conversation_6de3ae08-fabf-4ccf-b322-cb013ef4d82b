import { useState, useEffect } from 'react'
import { useAppStore } from './store'
import IconBar from './components/IconBar'
import MobileNavBar from './components/MobileNavBar'
import Sidebar from './components/Sidebar'
import ChatArea from './components/ChatArea'
import HistoryPage from './pages/HistoryPage'
import SettingsPage from './pages/SettingsPage'
import PerformanceMonitor from './components/PerformanceMonitor'
import { ArtifactsSidebar } from './components/artifacts/ArtifactsSidebar'
import { ToastProvider } from './components/artifacts/controls/ArtifactToast'
import ModelChangeToast from './components/ModelChangeToast'
import { HashRouter as Router, Routes, Route } from 'react-router-dom'

function App() {
  const { sidebarOpen, setSidebarOpen, settings, updateSettings } = useAppStore()
  const [showPerformanceMonitor, setShowPerformanceMonitor] = useState(false)
  const [modelChangeToast, setModelChangeToast] = useState<{
    isVisible: boolean
    newModel: string
    previousModel: string
  }>({
    isVisible: false,
    newModel: '',
    previousModel: ''
  })

  // Listen for model change events
  useEffect(() => {
    const handleModelChange = (event: CustomEvent) => {
      const { newModel, previousModel } = event.detail
      setModelChangeToast({
        isVisible: true,
        newModel,
        previousModel
      })
    }

    window.addEventListener('modelChanged', handleModelChange as EventListener)
    return () => {
      window.removeEventListener('modelChanged', handleModelChange as EventListener)
    }
  }, [])

  const handleRevertModel = async () => {
    // Revert to previous model
    updateSettings({ selectedModel: modelChangeToast.previousModel })

    // Save to electron store if available
    if (window.electronAPI?.settings) {
      await window.electronAPI.settings.set('app-settings', {
        ...settings,
        selectedModel: modelChangeToast.previousModel
      })
    }

    setModelChangeToast(prev => ({ ...prev, isVisible: false }))
  }

  const handleDismissToast = () => {
    setModelChangeToast(prev => ({ ...prev, isVisible: false }))
  }

  const handleOptOutToast = async () => {
    // Disable future model change notifications
    updateSettings({ showModelChangeNotifications: false })

    // Save to electron store if available
    if (window.electronAPI?.settings) {
      await window.electronAPI.settings.set('app-settings', {
        ...settings,
        showModelChangeNotifications: false
      })
    }

    setModelChangeToast(prev => ({ ...prev, isVisible: false }))
  }

  return (
    <Router>
      <ToastProvider>
        <div className="h-screen flex flex-col md:flex-row bg-gray-900 text-white font-sans antialiased selection:bg-primary/60 overflow-hidden">

        {/* Mobile Navigation Bar */}
        <MobileNavBar />

        {/* Desktop Layout */}
        <div className="flex flex-1 min-h-0">
          {/* VSCode-style Icon Bar */}
          <IconBar />

          {/* Sidebar */}
          <div className={`
            ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}
            md:translate-x-0 transition-transform duration-300 ease-in-out
            fixed md:relative z-40 h-full
            ${sidebarOpen ? 'left-0' : 'left-0'}
            md:left-0
          `}>
            <Sidebar />
          </div>

          {/* Overlay for mobile */}
          {sidebarOpen && (
            <div
              className="md:hidden fixed inset-0 bg-black/50 z-30"
              onClick={() => setSidebarOpen(false)}
            />
          )}

          {/* Main content area */}
          <div className="flex-1 flex flex-col min-w-0 h-full">
            <Routes>
              <Route path="/" element={<ChatArea />} />
              <Route path="/history" element={<HistoryPage />} />
              <Route path="/settings" element={<SettingsPage />} />
            </Routes>
          </div>

          {/* Artifacts Sidebar */}
          <ArtifactsSidebar />
        </div>

        {/* Performance Monitor */}
        <PerformanceMonitor
          isVisible={showPerformanceMonitor}
          onToggle={() => setShowPerformanceMonitor(!showPerformanceMonitor)}
        />

        {/* Model Change Toast */}
        <ModelChangeToast
          isVisible={modelChangeToast.isVisible}
          newModel={modelChangeToast.newModel}
          previousModel={modelChangeToast.previousModel}
          onRevert={handleRevertModel}
          onDismiss={handleDismissToast}
          onOptOut={handleOptOutToast}
        />
        </div>
      </ToastProvider>
    </Router>
  )
}

export default App
