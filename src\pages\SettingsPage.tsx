import React, { useState, useEffect } from 'react'
import { useAppStore } from '../store'
import { useNetworkStore } from '../stores/networkStore'
import { ArrowLeft, Key, Settings, User, FileText, Download, Folder, RefreshCw } from '../components/Icons'
import { useNavigate } from 'react-router-dom'
import ModelSelector from '../components/ModelSelector'

// Private Mode Model Selector Component
const PrivateModeModelSelector: React.FC<{
  selectedModel?: string
  onModelSelect: (modelId: string) => void
}> = ({ selectedModel, onModelSelect }) => {
  const { localModels, localModelsAvailable } = useNetworkStore()
  const [isOpen, setIsOpen] = useState(false)

  const selectedModelInfo = localModels.find(m => m.id === selectedModel)

  if (!localModelsAvailable || localModels.length === 0) {
    return (
      <div className="u1-input-field flex items-center justify-between opacity-50">
        <span className="text-gray-500">No local models available</span>
        <span className="text-xs text-gray-500">Install Ollama or LM Studio</span>
      </div>
    )
  }

  return (
    <div className="relative">
      <button
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        className="u1-input-field w-full flex items-center justify-between"
      >
        <div className="flex items-center gap-2 min-w-0">
          {selectedModelInfo ? (
            <>
              <span className="text-xs bg-primary/20 text-primary px-2 py-1 rounded">
                {selectedModelInfo.provider}
              </span>
              <span className="truncate">{selectedModelInfo.name}</span>
            </>
          ) : (
            <span className="text-gray-500">Select a local model...</span>
          )}
        </div>
        <i className={`fa-solid fa-chevron-down text-sm transition-transform ${isOpen ? 'rotate-180' : ''}`}></i>
      </button>

      {isOpen && (
        <>
          <div className="absolute top-full left-0 right-0 mt-1 bg-gray-800 border border-gray-700 rounded-lg shadow-xl z-50 max-h-64 overflow-y-auto">
            <div className="p-2">
              {localModels.map((model) => (
                <button
                  key={model.id}
                  onClick={() => {
                    onModelSelect(model.id)
                    setIsOpen(false)
                  }}
                  className={`
                    w-full p-3 rounded-lg text-left transition-colors hover:bg-gray-700
                    ${model.id === selectedModel ? 'bg-primary/20 border border-primary/30' : ''}
                  `}
                >
                  <div className="flex items-center justify-between mb-1">
                    <span className="font-medium text-sm text-supplement1">{model.name}</span>
                    <span className="text-xs bg-gray-700 text-gray-300 px-2 py-1 rounded">
                      {model.provider}
                    </span>
                  </div>
                  <p className="text-xs text-gray-400">Local model - Free to use</p>
                </button>
              ))}
            </div>
          </div>
          <div
            className="fixed inset-0 z-40"
            onClick={() => setIsOpen(false)}
          />
        </>
      )}
    </div>
  )
}

const SettingsPage: React.FC = () => {
  const navigate = useNavigate()
  const { settings, updateSettings } = useAppStore()
  const [activeTab, setActiveTab] = useState('api')
  const [localSettings, setLocalSettings] = useState(settings)
  const [isLoading, setIsLoading] = useState(false)
  const [testResult, setTestResult] = useState<string | null>(null)
  const [chatloPath, setChatloPath] = useState('')
  const [storageInfo, setStorageInfo] = useState<any>(null)

  useEffect(() => {
    setLocalSettings(settings)
    loadChatloPath()
    loadStorageInfo()
  }, [settings])

  const loadChatloPath = async () => {
    try {
      if (window.electronAPI?.files) {
        const path = await window.electronAPI.files.getChatloFolderPath()
        setChatloPath(path)
      }
    } catch (error) {
      console.error('Error loading Chatlo path:', error)
    }
  }

  const loadStorageInfo = async () => {
    try {
      if (window.electronAPI?.files) {
        const files = await window.electronAPI.files.getIndexedFiles()
        const totalSize = files.reduce((sum, file) => sum + file.file_size, 0)
        setStorageInfo({
          totalFiles: files.length,
          totalSize: totalSize,
          fileTypes: files.reduce((acc, file) => {
            acc[file.file_type] = (acc[file.file_type] || 0) + 1
            return acc
          }, {} as Record<string, number>)
        })
      }
    } catch (error) {
      console.error('Error loading storage info:', error)
    }
  }

  const handleSave = async () => {
    try {
      setIsLoading(true)
      updateSettings(localSettings)

      if (window.electronAPI?.settings) {
        await window.electronAPI.settings.set('app-settings', localSettings)
      }

      setTestResult('✅ Settings saved successfully!')
      setTimeout(() => setTestResult(null), 3000)
    } catch (error) {
      console.error('Error saving settings:', error)
      setTestResult('❌ Failed to save settings')
      setTimeout(() => setTestResult(null), 3000)
    } finally {
      setIsLoading(false)
    }
  }

  const testApiKey = async () => {
    if (!localSettings.openRouterApiKey) {
      setTestResult('❌ Please enter an API key first')
      return
    }

    try {
      setIsLoading(true)
      setTestResult('Testing API key...')

      // Import the service dynamically to avoid circular dependencies
      const { openRouterService } = await import('../services/openrouter')
      openRouterService.setApiKey(localSettings.openRouterApiKey)

      const validation = await openRouterService.validateApiKey()

      if (validation.valid) {
        setTestResult('✅ API key is valid!')
      } else {
        setTestResult('❌ Invalid API key')
      }
    } catch (error) {
      console.error('Error testing API key:', error)
      setTestResult('❌ Failed to test API key')
    } finally {
      setIsLoading(false)
    }
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
  }

  const handleSelectFolder = async () => {
    try {
      setIsLoading(true)
      if (window.electronAPI?.files) {
        const result = await window.electronAPI.files.showOpenDialog({
          title: 'Select Chatlo Folder',
          properties: ['openDirectory'],
          defaultPath: chatloPath
        })

        if (!result.canceled && result.filePaths.length > 0) {
          const newPath = result.filePaths[0]
          setTestResult('Updating folder location...')

          await window.electronAPI.files.setChatloFolderPath(newPath)
          setChatloPath(newPath)
          await loadStorageInfo() // Refresh storage info

          setTestResult('✅ Folder location updated successfully!')
          setTimeout(() => setTestResult(null), 3000)
        }
      }
    } catch (error) {
      console.error('Error selecting folder:', error)
      setTestResult('❌ Failed to update folder location')
      setTimeout(() => setTestResult(null), 3000)
    } finally {
      setIsLoading(false)
    }
  }

  const handleIndexFiles = async () => {
    try {
      setIsLoading(true)
      if (window.electronAPI?.files) {
        await window.electronAPI.files.indexAllFiles()
        await loadStorageInfo() // Refresh storage info
        setTestResult('✅ Files indexed successfully!')
        setTimeout(() => setTestResult(null), 3000)
      }
    } catch (error) {
      console.error('Error indexing files:', error)
      setTestResult('❌ Failed to index files')
      setTimeout(() => setTestResult(null), 3000)
    } finally {
      setIsLoading(false)
    }
  }

  const tabs = [
    { id: 'api', label: 'API Setup', icon: Key },
    { id: 'preferences', label: 'User Preferences', icon: Settings },
    { id: 'data', label: 'Data Management', icon: FileText },
    { id: 'profile', label: 'User Profile', icon: User },
  ]

  return (
    <div className="h-screen flex flex-col bg-gray-900 text-white">
      {/* Header */}
      <header className="flex items-center gap-4 h-16 px-6 border-b border-tertiary/30 bg-gray-800/60 backdrop-blur-lg">
        <button
          onClick={() => navigate('/')}
          className="u1-button-ghost"
        >
          <ArrowLeft className="h-4 w-4" />
        </button>
        <div className="flex items-center gap-2">
          <Settings className="h-5 w-5 text-primary" />
          <h1 className="text-xl font-semibold text-supplement1">Settings</h1>
        </div>
      </header>

      <div className="flex-1 flex">
        {/* Sidebar */}
        <nav className="w-64 border-r border-tertiary/30 bg-gray-800/30">
          <div className="p-4">
            <div className="space-y-1">
              {tabs.map((tab) => {
                const Icon = tab.icon
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`
                      w-full flex items-center gap-3 px-3 py-2 rounded-lg text-sm transition-colors
                      ${activeTab === tab.id
                        ? 'bg-primary text-gray-900 font-medium'
                        : 'text-gray-400 hover:text-supplement1 hover:bg-gray-700'
                      }
                    `}
                  >
                    <Icon className="h-4 w-4" />
                    {tab.label}
                  </button>
                )
              })}
            </div>
          </div>
        </nav>

        {/* Main Content */}
        <main className="flex-1 overflow-y-auto">
          <div className="max-w-4xl mx-auto p-8">
            {activeTab === 'api' && (
              <div className="space-y-6">
                <div>
                  <h2 className="text-2xl font-semibold mb-2 text-supplement1">API Configuration</h2>
                  <p className="text-gray-400">Configure your OpenRouter API key to access AI models.</p>
                </div>

                <div className="u1-card">
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium mb-2 text-supplement1">
                        OpenRouter API Key
                      </label>
                      <div className="flex gap-3">
                        <input
                          type="password"
                          value={localSettings.openRouterApiKey || ''}
                          onChange={(e) => setLocalSettings(prev => ({
                            ...prev,
                            openRouterApiKey: e.target.value
                          }))}
                          placeholder="sk-or-..."
                          className="u1-input-field flex-1"
                        />
                        <button
                          onClick={testApiKey}
                          disabled={isLoading}
                          className="u1-button-secondary disabled:opacity-50"
                        >
                          Test
                        </button>
                      </div>
                      {testResult && (
                        <p className="mt-2 text-sm text-supplement1">{testResult}</p>
                      )}
                    </div>

                    <div className="flex gap-3">
                      <button
                        onClick={handleSave}
                        disabled={isLoading}
                        className="u1-button-primary disabled:opacity-50"
                      >
                        Save Settings
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'data' && (
              <div className="space-y-6">
                <div>
                  <h2 className="text-2xl font-semibold mb-2 text-supplement1">Data Management</h2>
                  <p className="text-gray-400">Manage your files, conversations, and storage.</p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* File Storage */}
                  <div className="u1-card">
                    <h3 className="text-lg font-medium mb-4 text-supplement1">File Storage</h3>
                    <div className="space-y-4">
                      {/* Folder Selection */}
                      <div>
                        <label className="block text-sm font-medium mb-2 text-supplement1">
                          Chatlo Folder Location
                        </label>
                        <div className="flex gap-2">
                          <input
                            type="text"
                            value={chatloPath}
                            readOnly
                            className="u1-input-field flex-1 font-mono text-gray-300"
                          />
                          <button
                            onClick={handleSelectFolder}
                            className="px-3 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg text-sm font-medium transition-colors flex items-center gap-2"
                            title="Select folder"
                          >
                            <Folder className="h-4 w-4" />
                            Browse
                          </button>
                          <button
                            onClick={handleIndexFiles}
                            disabled={isLoading}
                            className="u1-button-primary disabled:opacity-50 flex items-center gap-2"
                            title="Index files"
                          >
                            <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
                            Index Files
                          </button>
                        </div>
                        {testResult && (
                          <p className="mt-2 text-sm text-supplement1">{testResult}</p>
                        )}
                      </div>

                      {/* Storage Stats */}
                      {storageInfo && (
                        <div className="pt-4 border-t border-gray-700">
                          <div className="grid grid-cols-2 gap-4 text-sm">
                            <div className="flex justify-between">
                              <span className="text-gray-400">Total Files:</span>
                              <span className="text-supplement1">{storageInfo.totalFiles}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-400">Total Size:</span>
                              <span className="text-supplement1">{formatFileSize(storageInfo.totalSize)}</span>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* File Types */}
                  {storageInfo && (
                    <div className="u1-card">
                      <h3 className="text-lg font-medium mb-4 text-supplement1">File Types</h3>
                      <div className="space-y-2">
                        {Object.entries(storageInfo.fileTypes).map(([type, count]) => (
                          <div key={type} className="flex justify-between text-sm">
                            <span className="text-gray-400 capitalize">{type}:</span>
                            <span className="text-supplement1">{count}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}

            {activeTab === 'preferences' && (
              <div className="space-y-6">
                <div>
                  <h2 className="text-2xl font-semibold mb-2 text-supplement1">User Preferences</h2>
                  <p className="text-gray-400">Configure your default models and preferences.</p>
                </div>

                <div className="u1-card">
                  <h3 className="text-lg font-medium mb-6 text-supplement1">Default Model Settings</h3>

                  <div className="space-y-6">
                    {/* Default Model for Regular Mode */}
                    <div>
                      <label className="block text-sm font-medium mb-2 text-supplement1">
                        Default Model (Regular Mode)
                      </label>
                      <p className="text-xs text-gray-400 mb-3">
                        This model will be selected by default when using external AI services.
                      </p>
                      <ModelSelector
                        selectedModel={localSettings.defaultModel}
                        onModelSelect={(modelId) => setLocalSettings(prev => ({
                          ...prev,
                          defaultModel: modelId
                        }))}
                        className="max-w-md"
                      />
                    </div>

                    {/* Default Model for Private Mode */}
                    <div>
                      <label className="block text-sm font-medium mb-2 text-supplement1">
                        Default Model (Private Mode)
                      </label>
                      <p className="text-xs text-gray-400 mb-3">
                        This local model will be selected by default when Private Mode is enabled.
                      </p>
                      <div className="max-w-md">
                        <PrivateModeModelSelector
                          selectedModel={localSettings.defaultPrivateModel}
                          onModelSelect={(modelId) => setLocalSettings(prev => ({
                            ...prev,
                            defaultPrivateModel: modelId
                          }))}
                        />
                      </div>
                    </div>

                    {/* Save Button */}
                    <div className="pt-4 border-t border-gray-700">
                      <button
                        onClick={handleSave}
                        disabled={isLoading}
                        className="u1-button-primary disabled:opacity-50"
                      >
                        Save Preferences
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'profile' && (
              <div className="space-y-6">
                <div>
                  <h2 className="text-2xl font-semibold mb-2 text-supplement1">User Profile</h2>
                  <p className="text-gray-400">Manage your profile and account settings.</p>
                </div>

                <div className="u1-card">
                  <div className="text-center py-8">
                    <User className="h-12 w-12 text-gray-500 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-400 mb-2">Coming Soon</h3>
                    <p className="text-sm text-gray-500">
                      User profile features will be available in a future update.
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>
        </main>
      </div>
    </div>
  )
}

export default SettingsPage
